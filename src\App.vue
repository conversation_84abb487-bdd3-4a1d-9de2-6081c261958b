<template>
    <router-view></router-view>
    <!-- Global Toast Notifications -->
    <ToastNotification />
    <!-- Global Confirmation Modal -->
    <ConfirmationModal :show="confirmationState.show" :title="confirmationState.title"
        :message="confirmationState.message" :confirm-text="confirmationState.confirmText"
        :cancel-text="confirmationState.cancelText" :type="confirmationState.type" :loading="confirmationState.loading"
        @confirm="handleConfirm" @cancel="handleCancel" />
</template>

<script>
import ConfirmationModal from '@/components/common/ConfirmationModal.vue'
import ToastNotification from '@/components/Toast/ToastNotification.vue'
import {
  confirmationState,
  useConfirmation,
} from '@/composables/useConfirmation'

export default {
    name: "App",
    components: {
        ToastNotification,
        ConfirmationModal
    },
    setup() {
        const { handleConfirm, handleCancel } = useConfirmation()

        return {
            confirmationState,
            handleConfirm,
            handleCancel
        }
    }
};
</script>