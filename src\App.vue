<template>
    <router-view></router-view>
    <!-- Global Toast Notifications -->
    <ToastNotification />

</template>

<script>
import ConfirmationModal from '@/components/common/ConfirmationModal.vue'
import ToastNotification from '@/components/Toast/ToastNotification.vue'
import {
  confirmationState,
  useConfirmation,
} from '@/composables/useConfirmation'

export default {
    name: "App",
    components: {
        ToastNotification,
        ConfirmationModal
    },
    setup() {
        const { handleConfirm, handleCancel } = useConfirmation()

        return {
            confirmationState,
            handleConfirm,
            handleCancel
        }
    }
};
</script>