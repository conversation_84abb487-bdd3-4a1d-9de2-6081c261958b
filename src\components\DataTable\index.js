// DataTable System - Self-contained and portable
// Export all components and composables for easy import

// Main Components
export { default as DataTable } from './DataTable.vue'
export { default as SmartDataTable } from './SmartDataTable.vue'
export { default as DataTableProvider } from './DataTableProvider.vue'

// Sub Components (only existing ones)
export { default as DataTablePagination } from './DataTablePagination.vue'
export { default as DataTableToolbar } from './DataTableToolbar.vue'
export { default as DataTableCell } from './DataTableCell.vue'
export { default as SearchableSelect } from './SearchableSelect.vue'
export { default as SimpleSelect } from './SimpleSelect.vue'
export { default as AdditionalFilters } from './AdditionalFilters.vue'

// UI Components (Self-contained)
export { default as ConfirmationModal } from './components/ConfirmationModal.vue'
export { default as ToastNotification } from './components/ToastNotification.vue'

// Core Composables
export { useDataTable } from './composables/useDataTable'
export { useDataTableCrud } from './composables/useDataTableCrud'
export { useDataTablePagination } from './composables/useDataTablePagination'
export { useDataTableExport } from './composables/useDataTableExport'
export { useSmartDataTable } from './composables/useSmartDataTable'

// Legacy composables (for backward compatibility)
export { useDataTableFilter } from './composables/useDataTableFilter'
export { useDataTableSort } from './composables/useDataTableSort'

// UI System Composables (Self-contained)
export { confirmationState, useDataTableConfirmation } from './composables/useDataTableConfirmation'
export { toastState, useDataTableToast } from './composables/useDataTableToast'

// Utilities
export * from './utils/dataTableUtils'
export * from './utils/exportUtils'

// Export as default for easy import
export default DataTable
