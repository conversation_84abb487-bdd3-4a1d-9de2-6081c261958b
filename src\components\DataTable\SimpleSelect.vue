<template>
  <div class="relative" ref="selectContainer">
    <!-- Main Select Button -->
    <button
      type="button"
      @click="toggleDropdown"
      class="w-full px-2 py-1 text-xs border border-gray-300 rounded bg-white text-left focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-150"
      :class="{
        'border-indigo-300 ring-1 ring-indigo-500': isOpen,
        'text-gray-900 font-medium': selectedLabel,
        'text-gray-500': !selectedLabel
      }"
    >
      <div class="flex items-center justify-between">
        <span class="truncate">{{ selectedLabel || placeholder }}</span>
        <div class="flex items-center space-x-1">
          <!-- Clear Button -->
          <button
            v-if="clearable && modelValue !== '' && modelValue !== null && modelValue !== undefined"
            @click.stop="clearSelection"
            class="p-0.5 text-gray-400 hover:text-gray-600 rounded transition-colors"
            type="button"
          >
            <XMarkIcon class="h-3 w-3" />
          </button>
          <!-- Dropdown Arrow -->
          <ChevronDownIcon 
            class="h-3 w-3 text-gray-400 transition-transform duration-150" 
            :class="{ 'rotate-180': isOpen }" 
          />
        </div>
      </div>
    </button>

    <!-- Dropdown Menu -->
    <div
      v-if="isOpen"
      class="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto"
    >
      <!-- Search Input (if searchable) -->
      <div v-if="searchable" class="p-2 border-b border-gray-200">
        <input
          ref="searchInput"
          v-model="searchTerm"
          type="text"
          :placeholder="searchPlaceholder"
          class="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
          @keydown.down.prevent="navigateDown"
          @keydown.up.prevent="navigateUp"
          @keydown.enter.prevent="selectHighlighted"
          @keydown.escape="closeDropdown"
        />
      </div>

      <!-- Options List -->
      <div class="py-1">
        <div
          v-for="(option, index) in filteredOptions"
          :key="option.value"
          @click="selectOption(option)"
          @mouseenter="highlightedIndex = index"
          class="px-3 py-2 text-xs cursor-pointer transition-colors duration-150"
          :class="{
            'bg-indigo-100 text-indigo-900': highlightedIndex === index,
            'bg-indigo-500 text-white': isSelected(option.value),
            'hover:bg-gray-100': !isSelected(option.value) && highlightedIndex !== index
          }"
        >
          <div class="flex items-center justify-between">
            <span class="font-medium">{{ option.label }}</span>
            <CheckIcon 
              v-if="isSelected(option.value)" 
              class="h-3 w-3 text-current flex-shrink-0 ml-2" 
            />
          </div>
        </div>
      </div>

      <!-- No Results -->
      <div v-if="filteredOptions.length === 0" class="p-3 text-center text-gray-500 text-xs">
        {{ searchTerm ? 'No results found' : 'No options available' }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import { ChevronDownIcon, XMarkIcon, CheckIcon } from '@heroicons/vue/24/outline'

// Props
const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: ''
  },
  options: {
    type: Array,
    default: () => []
  },
  placeholder: {
    type: String,
    default: 'Select an option...'
  },
  searchPlaceholder: {
    type: String,
    default: 'Search options...'
  },
  searchable: {
    type: Boolean,
    default: true
  },
  clearable: {
    type: Boolean,
    default: true
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'select', 'clear'])

// State
const isOpen = ref(false)
const searchTerm = ref('')
const highlightedIndex = ref(-1)
const selectContainer = ref(null)
const searchInput = ref(null)

// Computed
const selectedLabel = computed(() => {
  const option = props.options.find(opt => {
    // Handle both strict and loose equality
    return opt.value === props.modelValue || 
           (opt.value == props.modelValue && opt.value !== '')
  })
  
  console.log('🔍 SimpleSelect - Looking for value:', props.modelValue, typeof props.modelValue)
  console.log('🔍 SimpleSelect - Found option:', option)
  console.log('🔍 SimpleSelect - Selected label:', option?.label)
  
  return option?.label || ''
})

const filteredOptions = computed(() => {
  if (!searchTerm.value) {
    return props.options
  }
  
  return props.options.filter(option =>
    option.label.toLowerCase().includes(searchTerm.value.toLowerCase())
  )
})

// Methods
function isSelected(value) {
  return value === props.modelValue || (value == props.modelValue && value !== '')
}

function toggleDropdown() {
  if (isOpen.value) {
    closeDropdown()
  } else {
    openDropdown()
  }
}

function openDropdown() {
  isOpen.value = true
  highlightedIndex.value = -1
  searchTerm.value = ''
  
  // Focus search input if searchable
  if (props.searchable) {
    nextTick(() => {
      if (searchInput.value) {
        searchInput.value.focus()
      }
    })
  }
}

function closeDropdown() {
  isOpen.value = false
  searchTerm.value = ''
  highlightedIndex.value = -1
}

function selectOption(option) {
  console.log('🔍 SimpleSelect - Selecting option:', option)
  emit('update:modelValue', option.value)
  emit('select', option)
  closeDropdown()
}

function clearSelection() {
  console.log('🔍 SimpleSelect - Clearing selection')
  emit('update:modelValue', '')
  emit('clear')
  closeDropdown()
}

function selectHighlighted() {
  if (highlightedIndex.value >= 0 && highlightedIndex.value < filteredOptions.value.length) {
    selectOption(filteredOptions.value[highlightedIndex.value])
  }
}

function navigateDown() {
  if (highlightedIndex.value < filteredOptions.value.length - 1) {
    highlightedIndex.value++
  }
}

function navigateUp() {
  if (highlightedIndex.value > 0) {
    highlightedIndex.value--
  }
}

// Click outside to close
function handleClickOutside(event) {
  if (selectContainer.value && !selectContainer.value.contains(event.target)) {
    closeDropdown()
  }
}

// Watch for value changes
watch(() => props.modelValue, (newValue) => {
  console.log('🔍 SimpleSelect - Model value changed to:', newValue)
  console.log('🔍 SimpleSelect - New selected label:', selectedLabel.value)
})

// Lifecycle
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  console.log('🔍 SimpleSelect - Mounted with value:', props.modelValue)
  console.log('🔍 SimpleSelect - Initial label:', selectedLabel.value)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
/* Custom scrollbar for dropdown */
.overflow-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-auto::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.overflow-auto::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.overflow-auto::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
</style>
