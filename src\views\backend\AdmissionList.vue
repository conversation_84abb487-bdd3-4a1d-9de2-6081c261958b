<template>
  <div class="p-4">
    <SmartDataTable 
      title="Admission Management"
      :store="admissionStore"
      fetch-method="fetchAdmissions"
      delete-method="deleteAdmission"
      :column-config="columnConfig"
      :default-actions="['view', 'edit', 'delete']"
      @edit="handleEdit"
      @view="handleView"
    />
  </div>
</template>

<script setup>
import SmartDataTable from '@/components/DataTable/SmartDataTable.vue'
import useAdmissionStore from '@/stores/admission'
// import { useRouter } from 'vue-router'

const admissionStore = useAdmissionStore()
// const router = useRouter()

// Optional: Custom column configurations (only override what you need)
const columnConfig = {
  id: { width: '80px' },
  student_name: { title: 'Student Name', width: '200px' },
  email: { title: 'Email Address' },
  phone: { title: 'Phone Number' },
  course: { title: 'Applied Course' },
  status: { 
    type: 'badge',
    filterType: 'select',
    filterOptions: [
      { value: '', label: 'All Status' },
      { value: 'pending', label: 'Pending' },
      { value: 'approved', label: 'Approved' },
      { value: 'rejected', label: 'Rejected' },
      { value: 'enrolled', label: 'Enrolled' }
    ]
  },
  application_date: { title: 'Application Date', type: 'date' },
  // Auto-generated columns will be created for any other fields in your data
}

// Handle custom actions (edit, view) - delete is handled automatically
function handleEdit(admission) {
  console.log('Edit admission:', admission)
  // Implement edit logic or navigation
  // Example: router.push(`/admin/admissions/edit/${admission.id}`)
}

function handleView(admission) {
  console.log('View admission:', admission)
  // Implement view logic or navigation  
  // Example: router.push(`/admin/admissions/view/${admission.id}`)
}
</script>

<!-- 
THAT'S IT! Just ~50 lines vs 280+ lines before!

The SmartDataTable will automatically:
✅ Generate columns from your admission data structure
✅ Handle server-side pagination, sorting, filtering
✅ Provide CRUD operations (delete handled automatically)
✅ Use responsive design
✅ Include export functionality
✅ Apply smart defaults for everything

Your admission data might look like:
{
  id: 1,
  student_name: "John Doe",
  email: "<EMAIL>", 
  phone: "************",
  course: "Computer Science",
  status: "pending",
  application_date: "2024-01-15",
  address: "123 Main St",
  // ... any other fields will be auto-detected and displayed
}

The component will automatically:
- Detect 'email' field as email type
- Detect 'application_date' as date type  
- Detect 'status' as badge type (with your custom filter options)
- Show all other fields as text
- Skip created_at, updated_at, deleted_at fields
- Add actions column with view/edit/delete buttons
-->
