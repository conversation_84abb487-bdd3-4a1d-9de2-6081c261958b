<script setup>
import { ref } from 'vue'

import AddUser from '@/components/backend/users/AddUser.vue'
import EditUser from '@/components/backend/users/EditUser.vue'
import Viewuser from '@/components/backend/users/Viewuser.vue'
import SmartDataTable from '@/components/DataTable/SmartDataTable.vue'
import { toast } from '@/plugins/toast'
import useUserStore from '@/stores/user'

const userStore = useUserStore()
const showAddModal = ref(false)
const showEditModal = ref(false)
const showViewModal = ref(false)
const user = ref(null);

// User store initialized

// Column configuration - ONLY these columns will be shown
const columnConfig = {
    id: {
        title: 'ID',
        width: '80px',
        filterable: true  // Enable filter for ID
    },
    name: {
        title: 'Full Name',
        width: '200px',
        filterable: true  // Enable filter for name
    },
    email: {
        title: 'Email Address',
        filterable: true  // Enable filter for email
    },
    image_url_full: {
        title: 'Profile Image',
        type: 'image',
        width: '100px',
        filterable: false  // Disable filter for image
    },
    user_position: {
        title: 'Position',
        filterable: true  // Enable filter for position
    },
    status: {
        title: 'Status',
        type: 'badge',
        filterable: true,  // Enable filter for status
        filterType: 'select',
        filterOptions: [
            { value: '', label: 'All Status' },
            { value: '1', label: 'Active' },
            { value: '0', label: 'Inactive' }
        ],
        // Transform raw values (1, 0) to display values (Active, Inactive)
        transform: (value) => {
            if (value === 1 || value === '1') return 'Active'
            if (value === 0 || value === '0') return 'Inactive'
            return value // fallback for other values
        },
        badgeConfig: {
            'Active': 'bg-green-100 text-green-800',
            'Inactive': 'bg-red-100 text-red-800',
            // Keep original values as fallback
            '1': 'bg-green-100 text-green-800',
            '0': 'bg-red-100 text-red-800'
        }
    },
    //add
    // Only these 6 columns will be shown: ID, Name, Email, Image, Position, Status
    // Actions column will be auto-added because we have @edit and @view handlers
    // No other columns will appear (no created_at, updated_at, etc.)
}

// Optional: Custom table configuration (only if you want to override defaults)
const tableConfig = {
    pagination: {
        pageSize: 10,
        pageSizes: [5, 10, 15, 25, 50]
    },
    ui: {
        striped: true,
        hover: true,
        bordered: true,
        responsive: true,
        scrollable: {
            enabled: true,
            maxHeight: '500px', // Customize scroll height
            stickyColumns: {
                left: ['id'], // Make ID column sticky on left
                right: ['actions'] // Make actions column sticky on right
            }
        }
    },
    addButton: {
        enabled: true,
        text: 'Add New User',
        variant: 'primary',
        size: 'md'
    }
}

// Handle custom actions (edit, view) - delete is handled automatically
function handleEdit(user) {
    user.value = user;
    showEditModal.value = true;
}

function handleView(user) {
    user.value = user;
    showViewModal.value = true;
}

// Handle Add button click
function handleAdd() {
    showAddModal.value = true;
}

function handleCloseAddModal() {
    showAddModal.value = false // Hide modal
}



</script>

<template>
    <div class="p-4">
        <SmartDataTable title="User Management" :store="userStore" fetch-method="fetchUser" delete-method="deleteUser"
            :column-config="columnConfig" :table-config="tableConfig" @edit="handleEdit" @view="handleView"
            @add="handleAdd" />
        <AddUser v-if="showAddModal" @close="handleCloseAddModal" />
        <EditUser v-if="showEditModal" :user="user" @close="showEditModal = false" />
        <Viewuser v-if="showViewModal" :user="user" @close="showViewModal = false" />
    </div>
</template>
