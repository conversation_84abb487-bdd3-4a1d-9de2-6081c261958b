<script setup>
import {
  onMounted,
  ref,
} from 'vue'

import AddUser from '@/components/backend/users/AddUser.vue'
import EditUser from '@/components/backend/users/EditUser.vue'
import Viewuser from '@/components/backend/users/Viewuser.vue'
import {
  DataTableProvider,
  SmartDataTable,
} from '@/components/DataTable'
import useUserStore from '@/stores/user'

const userStore = useUserStore()

// Modal states
const showAddModal = ref(false)
const showEditModal = ref(false)
const showViewModal = ref(false)
const userData = ref(null)

// Data management states
const users = ref([])
const loading = ref(false)
const pagination = ref({
    current_page: 1,
    per_page: 10,
    total: 0,
    last_page: 1
})

// Search and filter states
const searchQuery = ref('')
const sortField = ref('')
const sortDirection = ref('asc');

// User store initialized

// Column configuration - ONLY these columns will be shown
const columnConfig = {
    id: {
        title: 'ID',
        width: '80px',
        filterable: true  
    },
    name: {
        title: 'Full Name',
        width: '200px',
        filterable: true  
    },
    email: {
        title: 'Email Address',
        filterable: true  
    },
    image_url_full: {
        title: 'Profile Image',
        type: 'image',
        width: '100px',
        filterable: false  
    },
    user_position: {
        title: 'Position',
        filterable: true  
    },
    status: {
        title: 'Status',
        type: 'badge',
        filterable: true,  
        filterType: 'select',
        filterOptions: [
            { value: '', label: 'All Status' },
            { value: '1', label: 'Active' },
            { value: '0', label: 'Inactive' }
        ],
        // Transform raw values (1, 0) to display values (Active, Inactive)
        transform: (value) => {
            if (value === 1 || value === '1') return 'Active'
            if (value === 0 || value === '0') return 'Inactive'
            return value // fallback for other values
        },
        badgeConfig: {
            'Active': 'bg-green-100 text-green-800',
            'Inactive': 'bg-red-100 text-red-800',
            // Keep original values as fallback
            '1': 'bg-green-100 text-green-800',
            '0': 'bg-red-100 text-red-800'
        }
    },
    //add
    // Only these 6 columns will be shown: ID, Name, Email, Image, Position, Status
    // Actions column will be auto-added because we have @edit and @view handlers
    // No other columns will appear (no created_at, updated_at, etc.)
}

// Optional: Custom table configuration (only if you want to override defaults)
const tableConfig = {
    pagination: {
        pageSize: 10,
        pageSizes: [5, 10, 15, 25, 50]
    },
    ui: {
        striped: true,
        hover: true,
        bordered: true,
        responsive: true,
        scrollable: {
            enabled: true,
            maxHeight: '500px', // Customize scroll height
            stickyColumns: {
                left: ['id'], // Make ID column sticky on left
                right: ['actions'] // Make actions column sticky on right
            }
        }
    },
    addButton: {
        enabled: true,
        text: 'Add New User',
        variant: 'primary',
        size: 'md'
    }
}

// Handle custom actions (edit, view) - delete is handled automatically
function handleEdit(user) {
    userData.value = user;
    showEditModal.value = true;
}

function handleView(user) {
    userData.value = user;
    showViewModal.value = true;
}

// Handle Add button click
function handleAdd() {
    showAddModal.value = true;
}

function handleCloseAddModal() {
    showAddModal.value = false // Hide modal
    fetchUsers() // Refresh data after adding
}

// 🔄 Data fetching and management functions
async function fetchUsers(page = 1) {
    loading.value = true
    try {
        const params = {
            page: page,
            per_page: pagination.value.per_page,
            search: searchQuery.value,
            sort_field: sortField.value,
            sort_direction: sortDirection.value
        }

        const response = await userStore.fetchUser(params)
        console.log(response);
        // Update data and pagination
        users.value = response.data || []
        pagination.value = {
            current_page: response.current_page || 1,
            per_page: response.per_page || 10,
            total: response.total || 0,
            last_page: response.last_page || 1
        }

    } catch (error) {
        console.error('Failed to fetch users:', error)
        users.value = []
    } finally {
        loading.value = false
    }
}

// 📄 Event handlers for DataTable
function handlePageChange(page) {
    pagination.value.current_page = page
    fetchUsers(page)
}

function handleSearch(query) {
    searchQuery.value = query
    pagination.value.current_page = 1 // Reset to first page
    fetchUsers(1)
}

function handleSort({ field, direction }) {
    sortField.value = field
    sortDirection.value = direction
    pagination.value.current_page = 1 // Reset to first page
    fetchUsers(1)
}

// 🎯 Custom delete functions for flexible endpoint configuration
async function handleDeleteUser(userId, userData) {
    console.log('Deleting user:', userId, userData)
    await userStore.deleteUser(userData)

}

async function handleBulkDeleteUsers(userIds, usersData) {
    console.log('Bulk deleting users:', userIds, usersData)

}

// 🚀 Initialize data on component mount
onMounted(() => {
    fetchUsers()
})

</script>

<template>
    <DataTableProvider>
        <div class="p-4">
            <SmartDataTable title="User Management" :data="users" :loading="loading" :pagination="pagination"
                :column-config="columnConfig" :table-config="tableConfig" :on-delete="handleDeleteUser"
                :on-bulk-delete="handleBulkDeleteUsers" @edit="handleEdit" @view="handleView" @add="handleAdd"
                @refresh="fetchUsers" @page-change="handlePageChange" @search="handleSearch" @sort="handleSort" />
            <AddUser v-if="showAddModal" @close="handleCloseAddModal" />
            <EditUser v-if="showEditModal" :user="userData" @close="showEditModal = false" />
            <Viewuser v-if="showViewModal" :user="userData" @close="showViewModal = false" />
        </div>
    </DataTableProvider>
</template>
