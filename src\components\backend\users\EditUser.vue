<script setup>
import {
  computed,
  ref,
  watch,
} from 'vue'

import { IMAGE_URL } from '@/api/endpoint'
import useUserStore from '@/stores/user'

const form = ref({
    name: '',
    email: '',
    user_position: '',
    password: '',
    status: '1',
    avatar: null,
})
const emit = defineEmits(['close']);

//import user props
const props = defineProps(['user']);

// Computed properties for cleaner code
const currentUserImage = computed(() => {
    if (props.user?.image_url) {
        return IMAGE_URL + props.user.image_url
    }
    return null
})

const isFormValid = computed(() => {
    return form.value.name && form.value.email
})

// Watch for changes to the user prop and update the form
watch(
    () => props.user,
    (user) => {
        if (user) {
            form.value.name = user.name || ''
            form.value.email = user.email || ''
            form.value.user_position = user.user_position || ''
            form.value.status = user.status != null ? String(user.status) : '1'
            form.value.avatar = null // Don't prefill file input
            form.value.password = '' // Don't prefill password for security
        }
    },
    { immediate: true }
)

async function updateUser() {
    if (!isFormValid.value) return;

    const formData = new FormData();
    formData.append('id', props.user.id); // Include user ID for update
    formData.append('name', form.value.name);
    formData.append('email', form.value.email);
    formData.append('user_position', form.value.user_position);
    formData.append('status', form.value.status);

    // Only include password if it's provided
    if (form.value.password) {
        formData.append('password', form.value.password);
    }

    // Only include avatar if a new one is selected
    if (form.value.avatar) {
        formData.append('avatar', form.value.avatar);
    }

    const isUpdated = await useUserStore().updateUser(formData);
    if (isUpdated) {
        emit('close');
    }
}

function onAvatarChange(event) {
    const file = event.target.files[0]
    form.value.avatar = file
}


</script>
<template>
    <transition name="fade">
        <div class="modal-overlay fixed inset-0 z-50 flex items-center justify-center backdrop-blur-sm bg-black/30"
            @click.self="$emit('close')">
            <div
                class="modal-content bg-white rounded-xl shadow-2xl w-full max-w-4xl relative animate-fadeIn overflow-hidden flex">
                <!-- Left side - User Profile -->
                <div class="modal-side bg-blue-600 text-white w-1/3 p-8 flex flex-col items-center justify-center">
                    <!-- Large User Profile Image -->
                    <div class="flex flex-col items-center text-center">
                        <div class="w-40 h-40 rounded-full overflow-hidden bg-white/20 flex items-center justify-center mb-6 border-4 border-white/30 shadow-xl">
                            <img v-if="currentUserImage"
                                 :src="currentUserImage"
                                 :alt="props.user?.name || 'User'"
                                 class="w-full h-full object-cover" />
                            <svg v-else xmlns="http://www.w3.org/2000/svg" width="80" height="80" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round"
                                stroke-linejoin="round" class="opacity-60">
                                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                                <circle cx="12" cy="7" r="4"></circle>
                            </svg>
                        </div>

                        <div class="text-center">
                            <h3 class="text-2xl font-bold mb-2">{{ props.user?.name || 'User Name' }}</h3>
                            <p class="text-blue-100 mb-1">{{ props.user?.user_position || 'Position' }}</p>
                            <p class="text-blue-200 text-sm">{{ props.user?.email || 'Email' }}</p>
                        </div>

                        <div class="mt-6 px-4 py-2 bg-white/10 rounded-lg backdrop-blur-sm">
                            <p class="text-xs text-blue-100 uppercase tracking-wide">Editing Profile</p>
                        </div>
                    </div>
                </div>

                <!-- Right side - Form -->
                <div class="w-2/3">
                    <div class="modal-header border-b border-gray-100 px-6 py-4 flex justify-between items-center">
                        <h2 class="text-xl font-semibold text-gray-800">Edit User</h2>
                        <button
                            class="close-btn text-gray-400 hover:text-gray-700 hover:bg-gray-100 rounded-full p-2 transition-colors"
                            @click="$emit('close')" aria-label="Close">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <line x1="18" y1="6" x2="6" y2="18"></line>
                                <line x1="6" y1="6" x2="18" y2="18"></line>
                            </svg>
                        </button>
                    </div>

                    <div class="modal-body p-6 max-h-[calc(100vh-200px)] overflow-y-auto">
                        <form @submit.prevent="updateUser">
                            <div class="grid grid-cols-2 gap-1">
                                <div class="mb-1 col-span-2">
                                    <label class="block text-gray-700 text-sm font-medium mb-2" for="name">Name</label>
                                    <input id="name" type="text" v-model="form.name"
                                        class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="Enter user name" required />
                                </div>

                                <div class="mb-1 col-span-2">
                                    <label class="block text-gray-700 text-sm font-medium mb-2"
                                        for="email">Email</label>
                                    <input id="email" type="email" v-model="form.email"
                                        class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="Enter user email" required />
                                </div>

                                <div class="mb-1">
                                    <label class="block text-gray-700 text-sm font-medium mb-2"
                                        for="position">Position</label>
                                    <input id="position" type="text" v-model="form.user_position"
                                        class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="Enter user position" />
                                </div>

                                <div class="mb-1">
                                    <label class="block text-gray-700 text-sm font-medium mb-2"
                                        for="status">Status</label>
                                    <select id="status" v-model="form.status"
                                        class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                        <option value="1">Active</option>
                                        <option value="0">Inactive</option>
                                    </select>
                                </div>

                                <div class="mb-1 col-span-2">
                                    <label class="block text-gray-700 text-sm font-medium mb-2"
                                        for="password">Password</label>
                                    <input id="password" type="password" v-model="form.password"
                                        class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="Leave blank to keep current password" />
                                    <p class="text-xs text-gray-500 mt-1">Leave empty to keep the current password</p>
                                </div>

                                <div class="mb-1 col-span-2">
                                    <label class="block text-gray-700 text-sm font-medium mb-2"
                                        for="avatar">Avatar</label>
                                    <input id="avatar" type="file" @change="onAvatarChange"
                                        class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
                                </div>
                            </div>

                            <div class="modal-footer flex justify-end gap-3 mt-1 pt-1 border-t border-gray-100">
                                <button type="button"
                                    class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors font-medium"
                                    @click="$emit('close')">
                                    Cancel
                                </button>
                                <button type="submit" :disabled="!isFormValid" :class="[
                                    'px-4 py-2 rounded-md transition-colors font-medium',
                                    isFormValid
                                        ? 'bg-blue-600 text-white hover:bg-blue-700'
                                        : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                ]">
                                    Update User
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </transition>
</template>

<style scoped>
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}

@keyframes fadeIn {
    from {
        transform: translateY(20px);
        opacity: 0;
    }

    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.animate-fadeIn {
    animation: fadeIn 0.3s ease-out;
}

.modal-side {
    background-image: linear-gradient(135deg, #4f46e5 0%, #3b82f6 100%);
    position: relative;
    overflow: hidden;
}

.modal-side::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E");
    opacity: 0.3;
}

.modal-content {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    height: auto;
    max-height: 90vh;
}

.close-btn {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Responsive adjustments */
@media (max-width: 767px) {
    .modal-content {
        flex-direction: column;
        max-width: 95%;
        max-height: 95vh;
    }

    .modal-side {
        width: 100%;
        height: 120px;
        padding: 20px;
        flex-direction: row;
        align-items: center;
    }

    .modal-side .mt-auto {
        margin-top: 0;
        margin-left: auto;
    }

    .modal-side svg {
        width: 50px;
        height: 50px;
    }

    .w-2\/3 {
        width: 100%;
    }
}
</style>
