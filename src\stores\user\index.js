import { defineStore } from 'pinia'

import api from '@/api/api'
import { toast } from '@/plugins/toast'

const useUserStore = defineStore("useUserStore", {
    state: () => ({
        user: [],
        totalPages: 1,
        totalRecords: 0,
        loading: false,
    }),

    actions: {
        async fetchUser(page = 1, limit = 10, sortBy = null, sortDir = null, filters = null) {
            this.loading = true;
            try {
                // Build query parameters
                let url = `/user-list?page=${page}&limit=${limit}`;

                // Add sorting if provided
                if (sortBy && sortDir) {
                    url += `&sort=${sortBy}&direction=${sortDir}`;
                    console.log(`Adding sort parameters: sort=${sortBy}, direction=${sortDir}`);
                }

                // Add filters if provided
                if (filters) {
                    Object.entries(filters).forEach(([key, value]) => {
                        if (value) {
                            url += `&filter[${key}]=${value}`;
                        }
                    });
                }

                const response = await api.get(url);
                console.log(response.data.data.data);

                if (response.data.data.data.length === 0) {
                    toast.info("No users found");
                    this.user = [];
                    return;
                }

                // Replace the array instead of using Object.assign
                this.user = response.data.data.data;
                this.totalPages = response.data.data.total_pages || 1;
                this.totalRecords = response.data.data.total || 0;

                return this.user;
            } catch (err) {
                console.error("Error fetching users:", err);
                toast.error("Failed to fetch users");
                this.user = [];
            } finally {
                this.loading = false;
            }
        },

        // Keep other methods as they are
        async createUser(userData) {
            this.loading = true;
            try {
                const response = await api.post("/createUser", userData);
                if (response.data) {
                    this.user.push(response.data.data.user);
                    toast.success(response.data.message);
                    return true;
                }
                return false;
            } catch (err) {
                console.error("Error creating user:", err);
                toast.error(err.response?.data?.message || "Failed to create user");
                return false;
            } finally {
                this.loading = false;
            }
        },

        async updateUser(userData) {
            this.loading = true;
            try {
                const response = await api.post("/editUser", userData);
                if (response.data) {
                    toast.success(response.data.message);
                    // Refresh the user list to get updated data
                    await this.fetchUser();
                    return true;
                }
                return false;
            } catch (err) {
                console.error("Error updating user:", err);
                toast.error(err.response?.data?.message || "Failed to update user");
                return false;
            } finally {
                this.loading = false;
            }
        },

        async deleteUser(id) {
            this.loading = true;
            try {
                alert('f');
                // const response = await api.delete(`/deleteUser/${id}`);
                if (response.data) {
                    // Remove user from the list
                    this.user = this.user.filter((user) => user.id !== id);
                    toast.success(response.data.message);
                    return true;
                }
                return false;
            } catch (err) {
                console.error("Error deleting user:", err);
                toast.error(err.response?.data?.message || "Failed to delete user");
                return false;
            } finally {
                this.loading = false;
            }
        },

        // Keep other methods unchanged
    },
});

export default useUserStore;
