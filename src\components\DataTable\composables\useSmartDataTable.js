import {
  onMounted,
  reactive,
  ref,
  watch,
} from 'vue'

/**
 * Smart DataTable Composable
 * Automatically handles server-side operations, column generation, and CRUD operations
 * Reduces boilerplate code in Vue components
 */
export function useSmartDataTable(options = {}) {
  // Initialize DataTable systems
  const { showDeleteConfirmation } = useDataTableConfirmation()
  const toast = useDataTableToast()

  // Extract options with defaults
  const {
    store,                    // Pinia store instance
    fetchMethod = 'fetchData', // Store method name for fetching data
    deleteMethod = 'deleteData', // Store method name for deleting data
    endpoint = '',            // API endpoint (if not using store)
    autoColumns = true,       // Auto-generate columns from data
    columnConfig = {},        // Custom column configurations
    tableConfig = {},         // Custom table configuration
    defaultActions = ['view', 'edit', 'delete'], // Default CRUD actions
    autoLoad = true,          // Auto-load data on mount
    searchDebounce = 500,     // Search debounce delay
  } = options

  // State
  const loading = ref(false)
  const data = ref([])
  const columns = ref([])
  const error = ref(null)
  const availableFilters = ref([])

  // Server-side parameters
  const serverParams = reactive({
    page: 1,
    pageSize: 10,
    sortBy: null,
    sortDir: null,
    search: '',
    filters: {}
  })

  // Pagination info
  const paginationInfo = reactive({
    currentPage: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0
  })

  // Default table configuration with smart defaults
  const defaultTableConfig = {
    pagination: {
      enabled: true,
      pageSize: 10,
      pageSizes: [5, 10, 25, 50, 100],
      showInfo: true,
      showSizeChanger: true
    },
    filtering: {
      enabled: true,
      global: true,
      column: true,
      debounce: searchDebounce
    },
    selection: {
      enabled: true,
      multiple: true,
      showSelectAll: true
    },
    ui: {
      striped: true,
      hover: true,
      stickyHeader: true,
      bordered: true,
      responsive: true,
      scrollable: {
        enabled: true,
        maxHeight: '600px',
        stickyColumns: {
          left: [], // e.g., ['id', 'name'] for sticky left columns
          right: [] // e.g., ['actions'] for sticky right columns
        }
      }
    },
    crud: {
      enabled: true,
      actions: defaultActions
    },
    export: {
      enabled: true,
      formats: ['csv', 'excel'],
      filename: 'data-export'
    },
    addButton: {
      enabled: true,
      text: 'Add New',
      variant: 'primary',
      size: 'md'
    }
  }

  // Merge configurations
  const finalTableConfig = reactive({ ...defaultTableConfig, ...tableConfig })

  /**
   * Generate columns from data structure or use provided columnConfig
   */
  async function generateColumns(sampleData) {
    if (!sampleData || sampleData.length === 0) return []

    // If columnConfig is provided, use ONLY those columns (no auto-generation)
    if (columnConfig && Object.keys(columnConfig).length > 0) {
      const specifiedColumns = []

      Object.entries(columnConfig).forEach(([key, config]) => {
        const column = {
          key,
          title: config.title || formatTitle(key),
          type: config.type || 'text',
          sortable: config.sortable !== undefined ? config.sortable : true,
          filterable: config.filterable !== undefined ? config.filterable : true,
          width: config.width,
          ...config
        }

        // Add filter options for status columns
        if (key === 'status' && config.type === 'badge') {
          column.filterType = 'select'
          if (!column.filterOptions) {
            column.filterOptions = [
              { value: '', label: 'All Status' },
              { value: 'active', label: 'Active' },
              { value: 'inactive', label: 'Inactive' }
            ]
          }
        }

        specifiedColumns.push(column)
      })

      // Add actions column if enabled
      if (defaultActions && defaultActions.length > 0) {
        const actionsColumn = await createActionsColumn()
        specifiedColumns.push(actionsColumn)
      }

      return specifiedColumns
    }

    // Auto-generation fallback (when no columnConfig provided)
    const firstRow = sampleData[0]
    const autoGeneratedColumns = []

    Object.keys(firstRow).forEach(key => {
      // Skip certain fields that shouldn't be displayed
      if ([
        'created_at', 'updated_at', 'deleted_at', 'email_verified_at',
        'password', 'remember_token', 'api_token', '_id'
      ].includes(key)) return

      const value = firstRow[key]
      let columnType = 'text'
      let filterable = true
      let sortable = true

      // Determine column type based on value
      if (typeof value === 'number') {
        columnType = 'number'
      } else if (typeof value === 'boolean') {
        columnType = 'boolean'
      } else if (key.includes('email')) {
        columnType = 'email'
      } else if (key.includes('date') || key.includes('time')) {
        columnType = 'date'
      } else if (key === 'status') {
        columnType = 'badge'
        filterable = true
      } else if (key.includes('image') || key.includes('avatar') || key.includes('photo')) {
        columnType = 'image'
        sortable = false
      }

      // Apply custom column config if provided
      const customConfig = columnConfig[key] || {}

      const column = {
        key,
        title: customConfig.title || formatTitle(key),
        type: customConfig.type || columnType,
        sortable: customConfig.sortable !== undefined ? customConfig.sortable : sortable,
        filterable: customConfig.filterable !== undefined ? customConfig.filterable : filterable,
        width: customConfig.width,
        ...customConfig
      }

      // Add filter options for status columns
      if (key === 'status' && columnType === 'badge') {
        column.filterType = 'select'
        column.filterOptions = [
          { value: '', label: 'All Status' },
          { value: 'active', label: 'Active' },
          { value: 'inactive', label: 'Inactive' }
        ]
      }

      autoGeneratedColumns.push(column)
    })

    // Add actions column if CRUD is enabled
    if (finalTableConfig.crud.enabled && defaultActions.length > 0) {
      const actionsColumn = await createActionsColumn()
      autoGeneratedColumns.push(actionsColumn)
    }

    return autoGeneratedColumns
  }

  /**
   * Generate available filters from columns
   */
  function generateAvailableFilters(columns) {
    const availableFilters = []

    columns.forEach(column => {
      if (column.filterable && column.type !== 'actions' && column.type !== 'selection') {
        const filter = {
          key: column.key,
          label: column.title || formatTitle(column.key),
          type: column.filterType || 'text'
        }

        // Add options for select filters
        if (filter.type === 'select' && column.filterOptions) {
          filter.options = column.filterOptions
        }

        availableFilters.push(filter)
      }
    })

    return availableFilters
  }

  /**
   * Create actions column configuration
   */
  async function createActionsColumn() {

    // Import icons dynamically
    let EyeIcon, PencilIcon, TrashIcon

    try {
      const icons = await import('@heroicons/vue/24/outline')
      EyeIcon = icons.EyeIcon
      PencilIcon = icons.PencilIcon
      TrashIcon = icons.TrashIcon
    } catch (err) {
      console.warn('Could not load heroicons:', err)
      // Fallback to simple text if icons fail to load
      EyeIcon = null
      PencilIcon = null
      TrashIcon = null
    }

    const actionConfig = {
      view: {
        icon: EyeIcon,
        class: 'text-blue-600 hover:text-blue-800',
        title: 'View'
      },
      edit: {
        icon: PencilIcon,
        class: 'text-green-600 hover:text-green-800',
        title: 'Edit'
      },
      delete: {
        icon: TrashIcon,
        class: 'text-red-600 hover:text-red-800',
        title: 'Delete'
      }
    }

    const actionsArray = defaultActions.map(action => ({
      key: action,
      icon: actionConfig[action]?.icon,
      title: actionConfig[action]?.title || formatTitle(action),
      class: actionConfig[action]?.class || 'text-gray-600 hover:text-gray-800'
    }))


    return {
      key: 'actions',
      title: 'Actions',
      type: 'actions',
      width: '120px',
      sortable: false,
      filterable: false,
      actions: actionsArray
    }
  }

  /**
   * Format field name to readable title
   */
  function formatTitle(key) {
    return key
      .replace(/_/g, ' ')
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, str => str.toUpperCase())
      .trim()
  }

  /**
   * Fetch data from store or API
   */
  async function fetchData(resetPage = false) {
    if (!store && !endpoint) {
      console.error('SmartDataTable: Either store or endpoint must be provided')
      return
    }

    loading.value = true
    error.value = null

    try {
      if (resetPage) {
        serverParams.page = 1
        paginationInfo.currentPage = 1
      }

      let result
      if (store && store[fetchMethod]) {
        // Use store method
        result = await store[fetchMethod](
          serverParams.page,
          serverParams.pageSize,
          serverParams.sortBy,
          serverParams.sortDir,
          {
            ...serverParams.filters,
            search: serverParams.search
          }
        )
      } else {
        // Direct API call (implement if needed)
        console.warn('❌ Store method not found!')
        console.warn('Store available:', !!store)
        console.warn('FetchMethod available:', !!store?.[fetchMethod])
        console.warn('Available methods:', store ? Object.keys(store).filter(key => typeof store[key] === 'function') : 'None')
        return
      }

      // Update data and pagination - automatically detect data property
      if (store) {
        // Try different common property names for data arrays
        const dataArray = store.data || store.user || store.admissions || store.posts ||
          store.products || store.categories || store.orders || store.items || []

        data.value = [...dataArray]
        paginationInfo.total = store.totalRecords || store.total || 0
        paginationInfo.totalPages = store.totalPages || 1
      }

      // Auto-generate columns if enabled and not already set
      if (autoColumns && columns.value.length === 0 && data.value.length > 0) {
        columns.value = await generateColumns(data.value)
        availableFilters.value = generateAvailableFilters(columns.value)
      }

    } catch (err) {
      error.value = err
      console.error('SmartDataTable fetch error:', err)
    } finally {
      loading.value = false
    }
  }

  /**
   * Handle sorting changes
   */
  async function handleSort(sortData) {
    if (sortData.column === null || sortData.direction === null) {
      serverParams.sortBy = null
      serverParams.sortDir = null
    } else {
      serverParams.sortBy = sortData.column
      serverParams.sortDir = sortData.direction
    }
    await fetchData(true)
  }

  /**
   * Handle filter changes
   */
  async function handleFilter(filterData) {
    console.log('🔍 Filter received:', filterData)

    if (filterData.key === 'global') {
      serverParams.search = filterData.value
    } else {
      // Fix: Check for empty string, null, undefined but NOT 0 (which is valid)
      if (filterData.value === '' || filterData.value === null || filterData.value === undefined) {
        delete serverParams.filters[filterData.key]
        console.log('🔍 Removed filter for:', filterData.key)
      } else {
        serverParams.filters[filterData.key] = filterData.value
        console.log('🔍 Set filter:', filterData.key, '=', filterData.value, typeof filterData.value)
      }
    }

    console.log('🔍 Current filters:', serverParams.filters)
    await fetchData(true)
  }

  /**
   * Handle clear all filters
   */
  async function handleClearFilters() {
    // Clear all filters and search
    serverParams.search = ''
    serverParams.filters = {}
    serverParams.page = 1 // Reset to first page
    paginationInfo.currentPage = 1
    await fetchData()
  }

  /**
   * Handle pagination changes
   */
  async function handlePageChange(pageData) {
    serverParams.page = pageData.page
    paginationInfo.currentPage = pageData.page
    if (pageData.pageSize) {
      serverParams.pageSize = pageData.pageSize
      paginationInfo.pageSize = pageData.pageSize
    }
    await fetchData()
  }

  /**
   * Handle CRUD actions
   */
  async function handleAction({ action, row }) {
    if (action === 'delete') {
      // Use beautiful confirmation modal
      const confirmed = await showDeleteConfirmation()

      if (confirmed) {
        try {
          if (store && store[deleteMethod]) {
            await store[deleteMethod](row.id)
            await fetchData() // Refresh data
            toast.success('Record deleted successfully')
          }
        } catch (err) {
          console.error('Delete error:', err)
          toast.error('Failed to delete record')
        }
      }
    } else if (action === 'edit') {
      // Emit edit event for parent to handle
      return { action: 'edit', row }
    } else if (action === 'view') {
      // Emit view event for parent to handle
      return { action: 'view', row }
    }
  }

  /**
   * Handle refresh
   */
  async function handleRefresh() {
    // Clear all filters and sorting
    serverParams.sortBy = null
    serverParams.sortDir = null
    serverParams.search = ''
    serverParams.filters = {}
    serverParams.page = 1

    paginationInfo.currentPage = 1

    await fetchData(true)
  }

  /**
   * Handle bulk delete
   */
  async function handleBulkDelete(selectedData) {
    if (!selectedData || selectedData.length === 0) {
      console.warn('No data provided for bulk delete')
      return
    }

    try {
      if (store && store[deleteMethod]) {
        // Delete each selected item
        for (const item of selectedData) {
          await store[deleteMethod](item.id)
        }

        // Refresh data after bulk delete
        await fetchData()

        console.log(`Successfully deleted ${selectedData.length} items`)
      } else {
        console.warn('Delete method not available in store')
      }
    } catch (error) {
      console.error('Bulk delete failed:', error)
      throw error
    }
  }

  /**
   * Set custom columns (override auto-generation)
   */
  function setColumns(customColumns) {
    columns.value = customColumns
  }

  /**
   * Update column configuration
   */
  function updateColumnConfig(key, config) {
    const columnIndex = columns.value.findIndex(col => col.key === key)
    if (columnIndex !== -1) {
      columns.value[columnIndex] = { ...columns.value[columnIndex], ...config }
    }
  }

  /**
   * Auto-load data on mount if enabled
   */
  if (autoLoad) {
    onMounted(() => {
      fetchData()
    })
  }

  /**
   * Watch for changes in store data to keep the table reactive
   */
  if (store) {
    watch(() => {
      // Try different common property names for data arrays
      return store.data || store.user || store.admissions || store.posts ||
        store.products || store.categories || store.orders || store.items || []
    }, async (newData) => {
      if (newData && newData.length > 0) {
        data.value = [...newData]

        // Update pagination info
        paginationInfo.total = store.totalRecords || store.total || 0
        paginationInfo.totalPages = store.totalPages || 1

        // Auto-generate columns if needed
        if (autoColumns && columns.value.length === 0) {
          columns.value = await generateColumns(newData)
          availableFilters.value = generateAvailableFilters(columns.value)
        }
      } else if (newData && newData.length === 0) {
        // Handle empty data
        data.value = []
      }
    }, { deep: true, immediate: true })
  }

  return {
    // State
    loading,
    data,
    columns,
    error,
    availableFilters,
    serverParams,
    paginationInfo,
    finalTableConfig,

    // Methods
    fetchData,
    generateColumns,
    formatTitle,
    handleSort,
    handleFilter,
    handleClearFilters,
    handlePageChange,
    handleAction,
    handleRefresh,
    handleBulkDelete,
    setColumns,
    updateColumnConfig
  }
}
