import { defineStore } from 'pinia'

import api from '@/api/api'
import { toast } from '@/plugins/toast'

const useAdmissionStore = defineStore("useAdmissionStore", {
    state: () => ({
        admissions: [],      // ← Main data array
        totalPages: 1,       // ← Required for pagination
        totalRecords: 0,     // ← Required for pagination info
        loading: false,
    }),

    actions: {
        // Required method with exact signature for SmartDataTable
        async fetchAdmissions(page = 1, limit = 10, sortBy = null, sortDir = null, filters = null) {
            this.loading = true;
            try {
                // Build query parameters
                let url = `/admissions?page=${page}&limit=${limit}`;

                // Add sorting if provided
                if (sortBy && sortDir) {
                    url += `&sort=${sortBy}&direction=${sortDir}`;
                    console.log(`Adding sort parameters: sort=${sortBy}, direction=${sortDir}`);
                }

                // Add filters if provided
                if (filters) {
                    Object.entries(filters).forEach(([key, value]) => {
                        if (value) {
                            url += `&filter[${key}]=${value}`;
                        }
                    });
                }

                const response = await api.get(url);
                console.log('Admissions response:', response.data.data.data);

                if (response.data.data.data.length === 0) {
                    toast.info("No admissions found");
                    this.admissions = [];
                    return;
                }

                // Update state with exact property names
                this.admissions = response.data.data.data;
                this.totalPages = response.data.data.total_pages || 1;
                this.totalRecords = response.data.data.total || 0;

                return this.admissions;
            } catch (err) {
                console.error("Error fetching admissions:", err);
                toast.error("Failed to fetch admissions");
                this.admissions = [];
            } finally {
                this.loading = false;
            }
        },

        // Required delete method for SmartDataTable
        async deleteAdmission(id) {
            this.loading = true;
            try {
                const response = await api.delete(`/admissions/${id}`);
                if (response.data) {
                    // Remove from local state
                    this.admissions = this.admissions.filter((admission) => admission.id !== id);
                    toast.success("Admission deleted successfully");
                    return true;
                }
                return false;
            } catch (err) {
                console.error("Error deleting admission:", err);
                toast.error("Failed to delete admission");
                return false;
            } finally {
                this.loading = false;
            }
        },

        // Optional: Additional methods for CRUD operations
        async createAdmission(admissionData) {
            this.loading = true;
            try {
                const response = await api.post("/admissions", admissionData);
                if (response.data) {
                    this.admissions.push(response.data.data.admission);
                    toast.success(response.data.message);
                    return true;
                }
                return false;
            } catch (err) {
                console.error("Error creating admission:", err);
                toast.error(err.response?.data?.message || "Failed to create admission");
                return false;
            } finally {
                this.loading = false;
            }
        },

        async updateAdmission(admissionData) {
            this.loading = true;
            try {
                const response = await api.put(`/admissions/${admissionData.id}`, admissionData);
                if (response.data) {
                    toast.success(response.data.message);
                    // Refresh the admission list to get updated data
                    await this.fetchAdmissions();
                    return true;
                }
                return false;
            } catch (err) {
                console.error("Error updating admission:", err);
                toast.error(err.response?.data?.message || "Failed to update admission");
                return false;
            } finally {
                this.loading = false;
            }
        }
    },
});

export default useAdmissionStore;
