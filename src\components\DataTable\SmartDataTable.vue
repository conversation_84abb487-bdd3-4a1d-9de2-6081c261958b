<template>
  <div class="smart-datatable w-full">
    <!-- Enhanced Title with Animation and Add Button -->
    <div v-if="title" class="mb-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold text-gray-900 mb-2 animate-fade-in">{{ title }}</h1>
          <div class="h-1 w-20 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full animate-slide-in"></div>
        </div>

        <!-- Add Button in Header -->
        <button v-if="finalTableConfig.addButton?.enabled" @click="$emit('add')" :class="getAddButtonClasses()"
          class="animate-fade-in">
          <PlusIcon class="h-5 w-5 mr-2" />
          {{ finalTableConfig.addButton.text || 'Add New' }}
        </button>
      </div>
    </div>

    <!-- Enhanced Table Container -->
    <div
      class="bg-white rounded-lg shadow-lg overflow-hidden border border-gray-200 transition-all duration-300 hover:shadow-xl">
      <DataTable :data="data" :columns="columns" :config="finalTableConfig" :loading="loading" :title="title"
        :available-filters="availableFilters" :current-filters="currentFilters" server-side @sort="handleSort"
        @filter="handleFilter" @clear-filters="handleClearFilters" @page-change="handlePageChange"
        @action="handleActionEvent" @add="$emit('add')" @refresh="handleRefresh" @bulk-delete="handleBulkDelete" />
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

import { PlusIcon } from '@heroicons/vue/24/outline'

import { useSmartDataTable } from './composables/useSmartDataTable.js'
import DataTable from './DataTable.vue'

// Props
const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  store: {
    type: Object,
    required: true
  },
  fetchMethod: {
    type: String,
    default: 'fetchUser'
  },
  deleteMethod: {
    type: String,
    default: 'deleteUser'
  },
  columnConfig: {
    type: Object,
    default: () => ({})
  },
  tableConfig: {
    type: Object,
    default: () => ({})
  },
  defaultActions: {
    type: Array,
    default: () => ['view', 'edit', 'delete']
  },
  autoColumns: {
    type: Boolean,
    default: true
  },
  autoLoad: {
    type: Boolean,
    default: true
  }
})

// Emits
const emit = defineEmits(['action', 'edit', 'view', 'delete', 'add'])

// Use Smart DataTable composable
const {
  loading,
  data,
  columns,
  availableFilters,
  serverParams,
  finalTableConfig,
  fetchData,
  handleSort,
  handleFilter,
  handleClearFilters,
  handlePageChange,
  handleAction,
  handleRefresh,
  handleBulkDelete,
  setColumns,
  updateColumnConfig
} = useSmartDataTable({
  store: props.store,
  fetchMethod: props.fetchMethod,
  deleteMethod: props.deleteMethod,
  columnConfig: props.columnConfig,
  tableConfig: props.tableConfig,
  defaultActions: props.defaultActions,
  autoColumns: props.autoColumns,
  autoLoad: props.autoLoad
})

// Convert server params filters to DataTable format
const currentFilters = computed(() => {
  const filters = {}
  if (serverParams.filters) {
    Object.entries(serverParams.filters).forEach(([key, value]) => {
      filters[key] = value
    })
  }
  console.log('🔍 SmartDataTable - Current filters for DataTable:', filters)
  return filters
})

// Handle action events and emit to parent
async function handleActionEvent(actionData) {
  const result = await handleAction(actionData)

  // Emit specific action events
  if (result) {
    emit('action', result)
    emit(result.action, result.row)
  }
}

// Get Add button classes based on configuration
function getAddButtonClasses() {
  const config = finalTableConfig.addButton || {}
  const variant = config.variant || 'primary'
  const size = config.size || 'md'

  // Base classes
  let classes = 'inline-flex items-center border shadow-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200 hover:shadow-md'

  // Size classes
  switch (size) {
    case 'sm':
      classes += ' px-3 py-2 text-sm'
      break
    case 'lg':
      classes += ' px-6 py-3 text-lg'
      break
    default: // md
      classes += ' px-4 py-2 text-base'
  }

  // Variant classes
  switch (variant) {
    case 'secondary':
      classes += ' border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-gray-500'
      break
    case 'success':
      classes += ' border-transparent text-white bg-green-600 hover:bg-green-700 focus:ring-green-500'
      break
    case 'danger':
      classes += ' border-transparent text-white bg-red-600 hover:bg-red-700 focus:ring-red-500'
      break
    case 'warning':
      classes += ' border-transparent text-white bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500'
      break
    case 'info':
      classes += ' border-transparent text-white bg-blue-600 hover:bg-blue-700 focus:ring-blue-500'
      break
    default: // primary
      classes += ' border-transparent text-white bg-indigo-600 hover:bg-indigo-700 focus:ring-indigo-500'
  }

  return classes
}

// Expose methods for parent component
defineExpose({
  fetchData,
  setColumns,
  updateColumnConfig,
  refresh: handleRefresh
})
</script>

<style scoped>
.smart-datatable {
  @apply w-full;
}

/* Title Animations */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in {
  from {
    width: 0;
  }

  to {
    width: 5rem;
  }
}

.animate-fade-in {
  animation: fade-in 0.6s ease-out;
}

.animate-slide-in {
  animation: slide-in 0.8s ease-out 0.3s both;
}

/* Container Enhancements */
.smart-datatable {
  animation: fade-in 0.5s ease-out;
}

/* Hover Effects */
.smart-datatable .bg-white:hover {
  transform: translateY(-2px);
}

/* Responsive Enhancements */
@media (max-width: 640px) {
  .smart-datatable h1 {
    font-size: 1.5rem;
  }
}
</style>
